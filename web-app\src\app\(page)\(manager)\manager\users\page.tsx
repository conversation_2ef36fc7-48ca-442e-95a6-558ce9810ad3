'use client'
import React from 'react';
import { UsersManagement } from '@/components/manager/users/UsersManagement';
import { SystemUser } from '@/types/manager';

export default function UsersPage() {
    // Mock data - in real app, this would come from API
    const mockUsers: SystemUser[] = [
        {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            fullName: 'Quản trị viên <PERSON>ố<PERSON>',
            role: 'admin',
            status: 'active',
            lastLogin: '2025-01-15T08:30:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        },
        {
            id: '2',
            username: 'manager1',
            email: '<EMAIL>',
            fullName: '<PERSON><PERSON><PERSON><PERSON>',
            role: 'manager',
            status: 'active',
            lastLogin: '2025-01-15T09:15:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        },
        {
            id: '3',
            username: 'doctor1',
            email: '<EMAIL>',
            fullName: '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
            role: 'doctor',
            department: 'Khoa Nội',
            status: 'active',
            lastLogin: '2025-01-15T07:45:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        },
        {
            id: '4',
            username: 'receptionist1',
            email: '<EMAIL>',
            fullName: 'Lễ tân Trần Thị B',
            role: 'receptionist',
            status: 'active',
            lastLogin: '2025-01-15T08:00:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        },
        {
            id: '5',
            username: 'patient1',
            email: '<EMAIL>',
            fullName: 'Bệnh nhân Lê Văn C',
            role: 'patient',
            status: 'active',
            lastLogin: '2025-01-14T15:30:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        },
        {
            id: '6',
            username: 'doctor2',
            email: '<EMAIL>',
            fullName: 'Bác sĩ Phạm Thị D',
            role: 'doctor',
            department: 'Khoa Ngoại',
            status: 'inactive',
            lastLogin: '2025-01-10T16:20:00Z',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-12-01T00:00:00Z'
        }
    ];

    return <UsersManagement users={mockUsers} />;
} 