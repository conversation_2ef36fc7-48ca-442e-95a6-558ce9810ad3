import type { Metadata } from "next";
import "./globals.css";
import ToastProvider from "@/components/providers/ToastProvider";
import StoreProvider from "@/redux/StoreProvider";
import React from "react";
import LayoutWrapper from "@/components/layouts/LayoutWrapper";

export const metadata: Metadata = {
  title: "Medical and Healthcare",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <LayoutWrapper>
          <StoreProvider>
            {children}
            <ToastProvider />
          </StoreProvider>
        </LayoutWrapper>
      </body>
    </html>
  );
}