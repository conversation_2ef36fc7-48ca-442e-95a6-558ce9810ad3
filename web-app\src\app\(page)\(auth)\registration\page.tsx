'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import Link from 'next/link';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import SocialLoginButtons from '@/components/social/SocialLoginButtons';
import { API_URL } from '@/utils/baseUrl';
import { ApiResponse } from '@/types/apiResonse';
import { ErrorResponse } from '@/types/errorResponse';
import { UserCreationResponse } from '@/types/user';

const Registration = () => {
    const router = useRouter();
    const [formData, setFormData] = useState({
        email: '',
        phone: '',
        password: '',
        firstName: '',
        lastName: '',
        dob: ''
    });
    const [loading, setLoading] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    useEffect(() => {
        const accessToken = localStorage.getItem('accessToken');
        if (accessToken) {
            router.push('/');
        } else {
            setLoading(false);
        }
    }, [router]);

    if (loading) {
        return <LoadingSpinner />;
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const validateForm = () => {
        const { email, phone, password, firstName, lastName, dob } = formData;

        if (!email || !phone || !password || !firstName || !lastName || !dob) {
            toast.error('Vui lòng điền đầy đủ tất cả thông tin!', {
                duration: 4000,
                position: 'top-right',
            });
            return false;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            toast.error('Email không đúng định dạng!', {
                duration: 4000,
                position: 'top-right',
            });
            return false;
        }

        const phoneRegex = /^[0-9]{10,11}$/;
        if (!phoneRegex.test(phone)) {
            toast.error('Số điện thoại phải có 10-11 chữ số!', {
                duration: 4000,
                position: 'top-right',
            });
            return false;
        }

        // Validate password
        if (password.length < 6) {
            toast.error('Mật khẩu phải có ít nhất 6 ký tự!', {
                duration: 4000,
                position: 'top-right',
            });
            return false;
        }

        return true;
    };

    const handleRegister = async (event: React.FormEvent) => {
        event.preventDefault();

        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        const loadingToast = toast.loading('Đang tạo tài khoản...', {
            position: 'top-right',
        });

        try {
            const response = await fetch(`${API_URL}/api/v1/users`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            // Debug logging
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            const responseData = await response.json();

            // Debug logging
            console.log('Response data:', responseData);

            toast.dismiss(loadingToast);

            // Kiểm tra nếu response không thành công (có lỗi)
            if (!response.ok) {
                // Backend trả về ErrorResponse khi có lỗi
                const errorResponse = responseData as ErrorResponse;

                console.log('=== ERROR RESPONSE DEBUG ===');
                console.log('Full error response:', JSON.stringify(errorResponse, null, 2));
                console.log('Error response keys:', Object.keys(errorResponse));
                console.log('Error message:', errorResponse.message);
                console.log('Error code:', errorResponse.code);
                console.log('Response status:', response.status);
                console.log('============================');

                // Xử lý message với nhiều fallback
                let errorMessage = 'Đăng ký thất bại. Vui lòng thử lại!';

                if (errorResponse.message && errorResponse.message.trim() !== '') {
                    const messageStr = errorResponse.message.toLowerCase();

                    if (messageStr.includes('user existed') ||
                        messageStr.includes('user_existed') ||
                        messageStr.includes('existed')) {
                        errorMessage = '📱 Số điện thoại này đã được đăng ký!\n\nVui lòng sử dụng số điện thoại khác hoặc đăng nhập nếu đây là tài khoản của bạn.';
                    } else {
                        // Hiển thị message gốc từ backend
                        errorMessage = `❌ ${errorResponse.message}`;
                    }
                } else {
                    // Fallback theo HTTP status code nếu không có message
                    console.log('No message from backend, using status code fallback');
                    switch (response.status) {
                        case 400:
                            errorMessage = '⚠️ Thông tin đăng ký không hợp lệ!\n\nVui lòng kiểm tra lại các trường thông tin.';
                            break;
                        case 409:
                            errorMessage = '📱 Số điện thoại hoặc email đã được sử dụng!\n\nVui lòng sử dụng thông tin khác.';
                            break;
                        case 500:
                            errorMessage = '🔧 Lỗi hệ thống!\n\nVui lòng thử lại sau ít phút.';
                            break;
                        default:
                            errorMessage = `❌ Đăng ký thất bại (Mã lỗi: ${response.status})!\n\nVui lòng thử lại.`;
                    }
                }

                console.log('Final error message:', errorMessage);

                toast.error(errorMessage, {
                    duration: 6000,
                    position: 'top-right',
                    style: {
                        background: '#DC2626',
                        color: '#fff',
                        whiteSpace: 'pre-line',
                        maxWidth: '400px',
                        fontSize: '14px',
                        lineHeight: '1.4'
                    },
                    iconTheme: {
                        primary: '#fff',
                        secondary: '#DC2626',
                    },
                });
                return;
            }

            // Response thành công - xử lý ApiResponse
            const result = responseData as ApiResponse<UserCreationResponse>;
            console.log('Success result:', result);

            // Chỉ hiển thị thành công khi thực sự thành công
            console.log('Registration successful!');

            toast.success(`🎉 Đăng ký thành công!\n\nChào mừng ${result.result?.firstName} ${result.result?.lastName} đến với hệ thống!\n\nBạn sẽ được chuyển đến trang đăng nhập.`, {
                duration: 4000,
                position: 'top-right',
                style: {
                    background: '#059669',
                    color: '#fff',
                    whiteSpace: 'pre-line',
                    maxWidth: '400px',
                    fontSize: '14px',
                    lineHeight: '1.4'
                },
                iconTheme: {
                    primary: '#fff',
                    secondary: '#059669',
                },
            });

            setTimeout(() => {
                router.push('/login');
            }, 2000);

        } catch (error: unknown) {
            console.error('Registration network error:', error);
            toast.dismiss(loadingToast);

            let networkErrorMessage = '🌐 Không thể kết nối đến server!\n\nVui lòng kiểm tra kết nối mạng và thử lại.';

            if (error instanceof Error) {
                console.error('Error details:', error.message);
                if (error.message.includes('fetch')) {
                    networkErrorMessage = '🌐 Lỗi kết nối mạng!\n\nVui lòng kiểm tra kết nối internet và thử lại.';
                } else if (error.message.includes('timeout')) {
                    networkErrorMessage = '⏱️ Kết nối quá chậm!\n\nVui lòng thử lại sau ít phút.';
                }
            }

            toast.error(networkErrorMessage, {
                duration: 6000,
                position: 'top-right',
                style: {
                    background: '#DC2626',
                    color: '#fff',
                    whiteSpace: 'pre-line',
                    maxWidth: '400px',
                    fontSize: '14px',
                    lineHeight: '1.4'
                },
                iconTheme: {
                    primary: '#fff',
                    secondary: '#DC2626',
                },
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <>
            <Toaster
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#FFFFFF',
                        color: '#1F2937',
                        fontSize: '14px',
                        borderRadius: '12px',
                        padding: '16px 20px',
                        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                        border: '1px solid #E5E7EB',
                    },
                }}
            />

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50">
                <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
                    <div className="max-w-xl w-full">
                        <div className="text-center mb-8">
                            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-600 to-emerald-600 rounded-2xl flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                            </div>
                            <h2 className="text-3xl font-bold text-gray-900 mb-2">Tạo tài khoản mới!</h2>
                            <p className="text-gray-600 text-base">Đăng ký để bắt đầu quản lý sức khỏe của bạn</p>
                        </div>

                        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 w-full max-w-2xl mx-auto">
                            <form className="space-y-6" onSubmit={handleRegister}>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2">
                                            Tên
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                            </div>
                                            <input
                                                type="text"
                                                id="firstName"
                                                name="firstName"
                                                value={formData.firstName}
                                                onChange={handleInputChange}
                                                disabled={isLoading}
                                                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                placeholder="Nhập tên"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2">
                                            Họ
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                            </div>
                                            <input
                                                type="text"
                                                id="lastName"
                                                name="lastName"
                                                value={formData.lastName}
                                                onChange={handleInputChange}
                                                disabled={isLoading}
                                                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                                placeholder="Nhập họ"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                                        Email
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                            </svg>
                                        </div>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            disabled={isLoading}
                                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                            placeholder="Nhập email"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                                        Số điện thoại
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                        <input
                                            type="tel"
                                            id="phone"
                                            name="phone"
                                            value={formData.phone}
                                            onChange={handleInputChange}
                                            disabled={isLoading}
                                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                            placeholder="Nhập số điện thoại"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="dob" className="block text-sm font-semibold text-gray-700 mb-2">
                                        Ngày sinh
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <input
                                            type="date"
                                            id="dob"
                                            name="dob"
                                            value={formData.dob}
                                            onChange={handleInputChange}
                                            disabled={isLoading}
                                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2">
                                        Mật khẩu
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                        </div>
                                        <input
                                            type={showPassword ? "text" : "password"}
                                            id="password"
                                            name="password"
                                            value={formData.password}
                                            onChange={handleInputChange}
                                            disabled={isLoading}
                                            className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                            placeholder="Nhập mật khẩu"
                                        />
                                        <button
                                            type="button"
                                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                            onClick={() => setShowPassword(!showPassword)}
                                        >
                                            {showPassword ? (
                                                <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                                </svg>
                                            ) : (
                                                <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                            )}
                                        </button>
                                    </div>
                                </div>

                                <div className="flex items-start">
                                    <input
                                        id="terms"
                                        name="terms"
                                        type="checkbox"
                                        required
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                                    />
                                    <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
                                        Tôi đồng ý với{' '}
                                        <Link href="/terms" className="text-blue-600 hover:text-blue-700 font-medium">
                                            Điều khoản sử dụng
                                        </Link>{' '}
                                        và{' '}
                                        <Link href="/privacy" className="text-blue-600 hover:text-blue-700 font-medium">
                                            Chính sách bảo mật
                                        </Link>
                                    </label>
                                </div>

                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className="cursor-pointer w-full bg-gray-900 text-white py-2.5 px-4 rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                >
                                    {isLoading ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Đang tạo tài khoản...
                                        </>
                                    ) : (
                                        'Tạo tài khoản'
                                    )}
                                </button>

                                <SocialLoginButtons
                                    isLoading={isLoading}
                                    dividerText="Hoặc đăng ký bằng"
                                />

                            </form>
                        </div>

                        <div className="text-center mt-6">
                            <p className="text-gray-600">
                                Đã có tài khoản?{' '}
                                <Link href="/login" className="text-blue-600 hover:text-blue-700 font-semibold">
                                    Đăng nhập ngay
                                </Link>
                            </p>
                        </div>

                        {/* Trust Indicators */}
                        <div className="mt-8 text-center">
                            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                    An toàn tuyệt đối
                                </div>
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    Đăng ký nhanh
                                </div>
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Miễn phí
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Registration;