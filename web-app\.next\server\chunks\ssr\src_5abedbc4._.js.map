{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/layouts/ManagerLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ManagerLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ManagerLayout() from the server but ManagerLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/manager/layouts/ManagerLayout.tsx <module evaluation>\",\n    \"ManagerLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kFACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/layouts/ManagerLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ManagerLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ManagerLayout() from the server but ManagerLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/manager/layouts/ManagerLayout.tsx\",\n    \"ManagerLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8DACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28manager%29/layout.tsx"], "sourcesContent": ["import { ManagerLayout } from '@/components/manager/layouts/ManagerLayout';\r\n\r\nexport default function ManagerLayoutWrapper({\r\n    children,\r\n}: {\r\n    children: React.ReactNode;\r\n}) {\r\n    return <ManagerLayout>{children}</ManagerLayout>;\r\n} "], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,qBAAqB,EACzC,QAAQ,EAGX;IACG,qBAAO,8OAAC,yJAAA,CAAA,gBAAa;kBAAE;;;;;;AAC3B", "debugId": null}}]}