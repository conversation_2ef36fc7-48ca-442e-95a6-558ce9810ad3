'use client'
import { useState, useEffect } from 'react';
import { Doctor, DoctorCreationRequest, Gender } from '@/types/doctor';
import { SpecialtyDetailResponse } from '@/types/specialty';
import { createDoctor } from '@/services/doctorService';
import { getSpecialties } from '@/services/specialtyService';
import toast from 'react-hot-toast';

interface DoctorModalProps {
    isOpen: boolean;
    doctor: Doctor | null;
    onClose: () => void;
    onSuccess: () => void;
}

export const DoctorModal = ({ isOpen, doctor, onClose, onSuccess }: DoctorModalProps) => {
    const [loading, setLoading] = useState(false);
    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);
    const [errors, setErrors] = useState<Record<string, string>>({});

    const [formData, setFormData] = useState({
        userName: '',
        userEmail: '',
        userPassword: '',
        specialtyId: '',
        licenseNumber: '',
        degree: '',
        consultationFee: '',
        isAvailable: true,
        gender: '',
        yearsOfExperience: '',
        bio: ''
    });

    useEffect(() => {
        if (isOpen) {
            fetchSpecialties();
            if (doctor) {
                // Populate form for editing (if needed)
                // setFormData({ ... });
            } else {
                // Reset form for new doctor
                setFormData({
                    userName: '',
                    userEmail: '',
                    userPassword: '',
                    specialtyId: '',
                    licenseNumber: '',
                    degree: '',
                    consultationFee: '',
                    isAvailable: true,
                    gender: '',
                    yearsOfExperience: '',
                    bio: ''
                });
            }
            setErrors({});
        }
    }, [isOpen, doctor]);

    const fetchSpecialties = async () => {
        try {
            const response = await getSpecialties(1, 100); // Get all specialties
            if (response.code === 200 && response.result) {
                setSpecialties(response.result.items || []);
            }
        } catch (error) {
            console.error('Error fetching specialties:', error);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }));

        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        // Required fields
        if (!formData.userName.trim()) {
            newErrors.userName = 'Tên bác sĩ là bắt buộc';
        }
        if (!formData.userEmail.trim()) {
            newErrors.userEmail = 'Email là bắt buộc';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.userEmail)) {
            newErrors.userEmail = 'Email không đúng định dạng';
        }
        if (!formData.userPassword.trim()) {
            newErrors.userPassword = 'Mật khẩu là bắt buộc';
        } else if (formData.userPassword.length < 6) {
            newErrors.userPassword = 'Mật khẩu phải có ít nhất 6 ký tự';
        }
        if (!formData.licenseNumber.trim()) {
            newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';
        }
        if (!formData.consultationFee.trim()) {
            newErrors.consultationFee = 'Phí khám là bắt buộc';
        } else if (isNaN(Number(formData.consultationFee)) || Number(formData.consultationFee) < 0) {
            newErrors.consultationFee = 'Phí khám phải là số dương';
        }
        if (!formData.yearsOfExperience.trim()) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm là bắt buộc';
        } else if (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm phải là số dương';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const request: DoctorCreationRequest = {
                UserName: formData.userName,
                UserEmail: formData.userEmail,
                UserPassword: formData.userPassword,
                SpecialtyId: formData.specialtyId ? Number(formData.specialtyId) : undefined,
                LicenseNumber: formData.licenseNumber,
                Degree: formData.degree || undefined,
                ConsultationFee: Number(formData.consultationFee),
                IsAvailable: formData.isAvailable,
                Gender: formData.gender ? Number(formData.gender) as Gender : undefined,
                YearsOfExperience: Number(formData.yearsOfExperience),
                Bio: formData.bio || undefined
            };

            const response = await createDoctor(request);

            if (response.code === 200 && response.result) {
                toast.success('🎉 Thêm bác sĩ thành công!', {
                    duration: 3000,
                    style: {
                        background: '#059669',
                        color: '#fff',
                    }
                });
                onSuccess();
                onClose();
            } else {
                toast.error(`❌ ${response.message || 'Thêm bác sĩ thất bại. Vui lòng thử lại!'}`, {
                    duration: 4000,
                    style: {
                        background: '#DC2626',
                        color: '#fff',
                    }
                });
            }
        } catch (error: any) {
            console.error('Error creating doctor:', error);

            let errorMessage = 'Đã xảy ra lỗi khi tạo bác sĩ!';
            if (error?.message) {
                errorMessage = `❌ ${error.message}`;
            }

            toast.error(errorMessage, {
                duration: 4000,
                style: {
                    background: '#DC2626',
                    color: '#fff',
                }
            });
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Tên bác sĩ */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Tên bác sĩ <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="userName"
                                type="text"
                                value={formData.userName}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.userName ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập tên bác sĩ"
                            />
                            {errors.userName && <p className="mt-1 text-sm text-red-600">{errors.userName}</p>}
                        </div>

                        {/* Email */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Email <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="userEmail"
                                type="email"
                                value={formData.userEmail}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.userEmail ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập email"
                            />
                            {errors.userEmail && <p className="mt-1 text-sm text-red-600">{errors.userEmail}</p>}
                        </div>

                        {/* Mật khẩu */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Mật khẩu <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="userPassword"
                                type="password"
                                value={formData.userPassword}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.userPassword ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập mật khẩu"
                            />
                            {errors.userPassword && <p className="mt-1 text-sm text-red-600">{errors.userPassword}</p>}
                        </div>

                        {/* Chuyên khoa */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Chuyên khoa</label>
                            <select
                                name="specialtyId"
                                value={formData.specialtyId}
                                onChange={handleInputChange}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Chọn chuyên khoa</option>
                                {specialties.map((specialty) => (
                                    <option key={specialty.id} value={specialty.id}>
                                        {specialty.specialtyName}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Số giấy phép hành nghề */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Số giấy phép hành nghề <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="licenseNumber"
                                type="text"
                                value={formData.licenseNumber}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.licenseNumber ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập số giấy phép"
                            />
                            {errors.licenseNumber && <p className="mt-1 text-sm text-red-600">{errors.licenseNumber}</p>}
                        </div>

                        {/* Bằng cấp */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Bằng cấp</label>
                            <input
                                name="degree"
                                type="text"
                                value={formData.degree}
                                onChange={handleInputChange}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Nhập bằng cấp"
                            />
                        </div>

                        {/* Phí khám */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Phí khám (VNĐ) <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="consultationFee"
                                type="number"
                                value={formData.consultationFee}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.consultationFee ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập phí khám"
                                min="0"
                            />
                            {errors.consultationFee && <p className="mt-1 text-sm text-red-600">{errors.consultationFee}</p>}
                        </div>

                        {/* Số năm kinh nghiệm */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Số năm kinh nghiệm <span className="text-red-500">*</span>
                            </label>
                            <input
                                name="yearsOfExperience"
                                type="number"
                                value={formData.yearsOfExperience}
                                onChange={handleInputChange}
                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.yearsOfExperience ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                placeholder="Nhập số năm kinh nghiệm"
                                min="0"
                            />
                            {errors.yearsOfExperience && <p className="mt-1 text-sm text-red-600">{errors.yearsOfExperience}</p>}
                        </div>

                        {/* Giới tính */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Giới tính</label>
                            <select
                                name="gender"
                                value={formData.gender}
                                onChange={handleInputChange}
                                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Chọn giới tính</option>
                                <option value="0">Nam</option>
                                <option value="1">Nữ</option>
                                <option value="2">Khác</option>
                            </select>
                        </div>

                        {/* Trạng thái hoạt động */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                            <div className="mt-2">
                                <label className="inline-flex items-center">
                                    <input
                                        type="checkbox"
                                        name="isAvailable"
                                        checked={formData.isAvailable}
                                        onChange={handleInputChange}
                                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">Đang hoạt động</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Tiểu sử */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Tiểu sử</label>
                        <textarea
                            name="bio"
                            value={formData.bio}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Nhập tiểu sử bác sĩ"
                        />
                    </div>

                    {/* Buttons */}
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            disabled={loading}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
                        >
                            Hủy
                        </button>
                        <button
                            type="submit"
                            disabled={loading}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
                        >
                            {loading && (
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            )}
                            <span>{loading ? 'Đang xử lý...' : (doctor ? 'Cập nhật' : 'Thêm')}</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}; 