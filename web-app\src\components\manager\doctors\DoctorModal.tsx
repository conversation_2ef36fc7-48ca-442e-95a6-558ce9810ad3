'use client'
import { useState, useEffect } from 'react';
import { Doctor, DoctorCreationRequest, Gender } from '@/types/doctor';
import { SpecialtyDetailResponse } from '@/types/specialty';
import { createDoctor } from '@/services/doctorService';
import { getSpecialties } from '@/services/specialtyService';
import toast from 'react-hot-toast';

interface DoctorModalProps {
    isOpen: boolean;
    doctor: Doctor | null;
    onClose: () => void;
    onSuccess: () => void;
}

export const DoctorModal = ({ isOpen, doctor, onClose, onSuccess }: DoctorModalProps) => {
    const [loading, setLoading] = useState(false);
    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);
    const [errors, setErrors] = useState<Record<string, string>>({});

    const [formData, setFormData] = useState({
        userName: '',
        userEmail: '',
        userPassword: '',
        specialtyId: '',
        licenseNumber: '',
        degree: '',
        consultationFee: '',
        isAvailable: true,
        gender: '',
        yearsOfExperience: '',
        bio: ''
    });

    useEffect(() => {
        if (isOpen) {
            fetchSpecialties();
            if (doctor) {
                // Populate form for editing (if needed)
                // setFormData({ ... });
            } else {
                // Reset form for new doctor
                setFormData({
                    userName: '',
                    userEmail: '',
                    userPassword: '',
                    specialtyId: '',
                    licenseNumber: '',
                    degree: '',
                    consultationFee: '',
                    isAvailable: true,
                    gender: '',
                    yearsOfExperience: '',
                    bio: ''
                });
            }
            setErrors({});
        }
    }, [isOpen, doctor]);

    const fetchSpecialties = async () => {
        try {
            const response = await getSpecialties(1, 100); // Get all specialties
            if (response.code === 200 && response.result) {
                setSpecialties(response.result.items || []);
            }
        } catch (error) {
            console.error('Error fetching specialties:', error);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
        }));

        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        // Required fields
        if (!formData.userName.trim()) {
            newErrors.userName = 'Tên bác sĩ là bắt buộc';
        }
        if (!formData.userEmail.trim()) {
            newErrors.userEmail = 'Email là bắt buộc';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.userEmail)) {
            newErrors.userEmail = 'Email không đúng định dạng';
        }
        if (!formData.userPassword.trim()) {
            newErrors.userPassword = 'Mật khẩu là bắt buộc';
        } else if (formData.userPassword.length < 6) {
            newErrors.userPassword = 'Mật khẩu phải có ít nhất 6 ký tự';
        }
        if (!formData.licenseNumber.trim()) {
            newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';
        }
        if (!formData.consultationFee.trim()) {
            newErrors.consultationFee = 'Phí khám là bắt buộc';
        } else if (isNaN(Number(formData.consultationFee)) || Number(formData.consultationFee) < 0) {
            newErrors.consultationFee = 'Phí khám phải là số dương';
        }
        if (!formData.yearsOfExperience.trim()) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm là bắt buộc';
        } else if (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0) {
            newErrors.yearsOfExperience = 'Số năm kinh nghiệm phải là số dương';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);

        try {
            const request: DoctorCreationRequest = {
                UserName: formData.userName,
                UserEmail: formData.userEmail,
                UserPassword: formData.userPassword,
                SpecialtyId: formData.specialtyId ? Number(formData.specialtyId) : undefined,
                LicenseNumber: formData.licenseNumber,
                Degree: formData.degree || undefined,
                ConsultationFee: Number(formData.consultationFee),
                IsAvailable: formData.isAvailable,
                Gender: formData.gender ? Number(formData.gender) as Gender : undefined,
                YearsOfExperience: Number(formData.yearsOfExperience),
                Bio: formData.bio || undefined
            };

            const response = await createDoctor(request);

            if (response.code === 200 && response.result) {
                toast.success('🎉 Thêm bác sĩ thành công!', {
                    duration: 3000,
                    style: {
                        background: '#059669',
                        color: '#fff',
                    }
                });
                onSuccess();
                onClose();
            } else {
                toast.error(`❌ ${response.message || 'Thêm bác sĩ thất bại. Vui lòng thử lại!'}`, {
                    duration: 4000,
                    style: {
                        background: '#DC2626',
                        color: '#fff',
                    }
                });
            }
        } catch (error: any) {
            console.error('Error creating doctor:', error);

            let errorMessage = 'Đã xảy ra lỗi khi tạo bác sĩ!';
            if (error?.message) {
                errorMessage = `❌ ${error.message}`;
            }

            toast.error(errorMessage, {
                duration: 4000,
                style: {
                    background: '#DC2626',
                    color: '#fff',
                }
            });
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Tên bác sĩ</label>
                        <input
                            name="name"
                            type="text"
                            defaultValue={doctor?.name || ''}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Khoa</label>
                        <select
                            name="department"
                            defaultValue={doctor?.department || departments[1]}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                        >
                            {departments.slice(1).map((dept) => (
                                <option key={dept} value={dept}>{dept}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Trạng thái</label>
                        <select
                            name="status"
                            defaultValue={doctor?.status || 'available'}
                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                        >
                            <option value="available">Có sẵn</option>
                            <option value="busy">Bận</option>
                            <option value="off">Nghỉ</option>
                        </select>
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        >
                            Hủy
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                        >
                            {doctor ? 'Cập nhật' : 'Thêm'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}; 