'use client'
import React from 'react';
import { DoctorsManagement } from '@/components/manager/doctors/DoctorsManagement';
import { Doctor } from '@/types/doctor';

export default function DoctorsPage() {
    // Mock data - in real app, this would come from API
    const mockDoctors: Doctor[] = [
        {
            id: '1',
            name: '<PERSON><PERSON><PERSON> s<PERSON>',
            department: 'Khoa Nội',
            schedule: {
                weekdays: '8:00 - 17:00',
                saturday: '8:00 - 12:00'
            },
            status: 'available',
            avatar: '/default-avatar.png'
        },
        {
            id: '2',
            name: '<PERSON><PERSON><PERSON> sĩ <PERSON>h<PERSON>',
            department: 'Khoa Ngoại',
            schedule: {
                weekdays: '8:00 - 17:00'
            },
            status: 'busy',
            avatar: '/default-avatar.png'
        },
        {
            id: '3',
            name: '<PERSON><PERSON><PERSON> s<PERSON>',
            department: 'Khoa Nhi',
            schedule: {
                weekdays: '8:00 - 16:00',
                saturday: '8:00 - 12:00',
                sunday: '8:00 - 12:00'
            },
            status: 'available',
            avatar: '/default-avatar.png'
        },
        {
            id: '4',
            name: '<PERSON><PERSON><PERSON>',
            department: '<PERSON><PERSON><PERSON>',
            schedule: {
                weekdays: '9:00 - 18:00'
            },
            status: 'off',
            avatar: '/default-avatar.png'
        }
    ];

    return <DoctorsManagement doctors={mockDoctors} />;
} 