{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/ScheduleManagement/ScheduleManagement.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Calendar, Plus, Search, Edit3, Trash2, CheckCircle } from 'lucide-react';\r\nimport { ScheduleItem, StatusType } from '@/hooks/doctor/types';\r\n\r\ninterface ScheduleManagementProps {\r\n  scheduleData: ScheduleItem[];\r\n  selectedDate: string;\r\n  searchTerm: string;\r\n  filterStatus: StatusType | 'all';\r\n  onDateChange: (date: string) => void;\r\n  onSearchChange: (term: string) => void;\r\n  onFilterChange: (status: StatusType | 'all') => void;\r\n  getStatusColor: (status: StatusType) => string;\r\n  getStatusText: (status: StatusType) => string;\r\n  getPriorityColor: (priority: string) => string;\r\n  getPriorityText: (priority: string) => string;\r\n}\r\n\r\nconst ScheduleManagement = ({\r\n  scheduleData,\r\n  selectedDate,\r\n  searchTerm,\r\n  filterStatus,\r\n  onDateChange,\r\n  onSearchChange,\r\n  onFilterChange,\r\n  getStatusColor,\r\n  getStatusText,\r\n  getPriorityColor,\r\n  getPriorityText,\r\n}: ScheduleManagementProps) => {\r\n  const filteredScheduleData = useMemo(() => {\r\n    return scheduleData.filter(item => {\r\n      const matchesSearch = searchTerm === '' || \r\n        item.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        item.phone.includes(searchTerm);\r\n      const matchesStatus = filterStatus === 'all' || item.status === filterStatus;\r\n      return matchesSearch && matchesStatus;\r\n    });\r\n  }, [scheduleData, searchTerm, filterStatus]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-2xl font-bold text-gray-800\">Quản lý Lịch hẹn</h2>\r\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\">\r\n          <Plus size={20} />\r\n          Thêm lịch hẹn\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"flex gap-4 items-center flex-wrap\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <input\r\n            type=\"date\"\r\n            value={selectedDate}\r\n            onChange={(e) => onDateChange(e.target.value)}\r\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n        </div>\r\n        <div className=\"relative flex-1 max-w-md\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Tìm theo tên bệnh nhân hoặc số điện thoại...\"\r\n            value={searchTerm}\r\n            onChange={(e) => onSearchChange(e.target.value)}\r\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n        </div>\r\n        <select\r\n          value={filterStatus}\r\n          onChange={(e) => onFilterChange(e.target.value as StatusType | 'all')}\r\n          className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n        >\r\n          <option value=\"all\">Tất cả trạng thái</option>\r\n          <option value=\"confirmed\">Đã xác nhận</option>\r\n          <option value=\"pending\">Chờ xử lý</option>\r\n          <option value=\"completed\">Hoàn thành</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Bệnh nhân</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Khoa</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Thời gian</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Triệu chứng</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Độ ưu tiên</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Trạng thái</th>\r\n                <th className=\"px-6 py-4 text-left text-sm font-semibold text-gray-700\">Thao tác</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"divide-y divide-gray-200\">\r\n              {filteredScheduleData.map((appointment) => (\r\n                <tr key={appointment.id} className=\"hover:bg-gray-50\">\r\n                  <td className=\"px-6 py-4\">\r\n                    <div>\r\n                      <div className=\"font-medium text-gray-900\">{appointment.patientName}</div>\r\n                      <div className=\"text-sm text-gray-500\">{appointment.phone}</div>\r\n                      {appointment.patientAge && (\r\n                        <div className=\"text-xs text-gray-400\">{appointment.patientAge} tuổi</div>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 text-sm text-gray-700\">{appointment.department}</td>\r\n                  <td className=\"px-6 py-4\">\r\n                    <div className=\"text-sm font-medium text-gray-900\">{appointment.time}</div>\r\n                    {appointment.duration && (\r\n                      <div className=\"text-xs text-gray-500\">{appointment.duration} phút</div>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 text-sm text-gray-700 max-w-xs\">\r\n                    <div className=\"truncate\" title={appointment.symptoms}>{appointment.symptoms}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4\">\r\n                    {appointment.priority && (\r\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(appointment.priority)}`}>\r\n                        {getPriorityText(appointment.priority)}\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-6 py-4\">\r\n                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`}>\r\n                      {getStatusText(appointment.status)}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-6 py-4\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <button className=\"text-blue-600 hover:text-blue-800 p-1 rounded\">\r\n                        <Edit3 size={16} />\r\n                      </button>\r\n                      <button className=\"text-red-600 hover:text-red-800 p-1 rounded\">\r\n                        <Trash2 size={16} />\r\n                      </button>\r\n                      {appointment.status === 'confirmed' && (\r\n                        <button className=\"text-green-600 hover:text-green-800 p-1 rounded\">\r\n                          <CheckCircle size={16} />\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        {filteredScheduleData.length === 0 && (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            Không tìm thấy lịch hẹn nào phù hợp với bộ lọc\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScheduleManagement;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAoBA,MAAM,qBAAqB,CAAC,EAC1B,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,eAAe,EACS;IACxB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,OAAO,aAAa,MAAM,CAAC,CAAA;YACzB,MAAM,gBAAgB,eAAe,MACnC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,KAAK,CAAC,QAAQ,CAAC;YACtB,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,MAAM,KAAK;YAChE,OAAO,iBAAiB;QAC1B;IACF,GAAG;QAAC;QAAc;QAAY;KAAa;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;;;;;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;gCAAmE,MAAM;;;;;;0CAC3F,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,8OAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,8OAAC;gCAAO,OAAM;0CAAY;;;;;;;;;;;;;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;;;;;;;;;;;;8CAG5E,8OAAC;oCAAM,WAAU;8CACd,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAA6B,YAAY,WAAW;;;;;;0EACnE,8OAAC;gEAAI,WAAU;0EAAyB,YAAY,KAAK;;;;;;4DACxD,YAAY,UAAU,kBACrB,8OAAC;gEAAI,WAAU;;oEAAyB,YAAY,UAAU;oEAAC;;;;;;;;;;;;;;;;;;8DAIrE,8OAAC;oDAAG,WAAU;8DAAmC,YAAY,UAAU;;;;;;8DACvE,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEAAqC,YAAY,IAAI;;;;;;wDACnE,YAAY,QAAQ,kBACnB,8OAAC;4DAAI,WAAU;;gEAAyB,YAAY,QAAQ;gEAAC;;;;;;;;;;;;;8DAGjE,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;wDAAW,OAAO,YAAY,QAAQ;kEAAG,YAAY,QAAQ;;;;;;;;;;;8DAE9E,8OAAC;oDAAG,WAAU;8DACX,YAAY,QAAQ,kBACnB,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,YAAY,QAAQ,GAAG;kEACpG,gBAAgB,YAAY,QAAQ;;;;;;;;;;;8DAI3C,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,YAAY,MAAM,GAAG;kEAChG,cAAc,YAAY,MAAM;;;;;;;;;;;8DAGrC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,0MAAA,CAAA,QAAK;oEAAC,MAAM;;;;;;;;;;;0EAEf,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;;;;;;4DAEf,YAAY,MAAM,KAAK,6BACtB,8OAAC;gEAAO,WAAU;0EAChB,cAAA,8OAAC,2NAAA,CAAA,cAAW;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;2CA1CpB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;oBAoD9B,qBAAqB,MAAM,KAAK,mBAC/B,8OAAC;wBAAI,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;AAO1D;uCAEe", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/AppointmentRequests/AppointmentRequests.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport {User, CheckCircle, XCircle, Edit3 } from 'lucide-react';\r\nimport { AppointmentRequest, StatusType } from '@/hooks/doctor/types';\r\n\r\n\r\ninterface AppointmentRequestsProps {\r\n  appointmentRequests: AppointmentRequest[];\r\n  getStatusColor: (status: StatusType) => string;\r\n  getStatusText: (status: StatusType) => string;\r\n  getPriorityColor: (priority: string) => string;\r\n  getPriorityText: (priority: string) => string;\r\n}\r\n\r\nconst AppointmentRequests = ({\r\n  appointmentRequests,\r\n  getStatusColor,\r\n  getStatusText,\r\n  getPriorityColor,\r\n  getPriorityText,\r\n}: AppointmentRequestsProps) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-2xl font-bold text-gray-800\">Y<PERSON>u c<PERSON><PERSON><PERSON> bệ<PERSON></h2>\r\n        <div className=\"flex gap-2\">\r\n          <span className=\"bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium\">\r\n            {appointmentRequests.filter(req => req.status === 'pending').length} yêu cầu chờ xử lý\r\n          </span>\r\n          <span className=\"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium\">\r\n            {appointmentRequests.filter(req => req.urgency === 'high').length} khẩn cấp\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid gap-4\">\r\n        {appointmentRequests.map((request) => (\r\n          <div key={request.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n            <div className=\"flex justify-between items-start mb-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                  <User className=\"text-blue-600\" size={24} />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">{request.patientName}</h3>\r\n                  <p className=\"text-sm text-gray-500\">{request.phone}</p>\r\n                  {request.patientAge && (\r\n                    <p className=\"text-xs text-gray-400\">{request.patientAge} tuổi</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                {request.urgency && (\r\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request.urgency)}`}>\r\n                    {getPriorityText(request.urgency)}\r\n                  </span>\r\n                )}\r\n                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>\r\n                  {getStatusText(request.status)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Ngày yêu cầu: <span className=\"font-medium\">{request.requestDate}</span></p>\r\n                <p className=\"text-sm text-gray-600\">Thời gian mong muốn: <span className=\"font-medium\">{request.requestedTime}</span></p>\r\n                <p className=\"text-sm text-gray-600\">Khoa: <span className=\"font-medium\">{request.department}</span></p>\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600 mb-2\">Triệu chứng:</p>\r\n                <p className=\"text-sm text-gray-800\">{request.symptoms}</p>\r\n              </div>\r\n            </div>\r\n\r\n            {request.status === 'pending' && (\r\n              <div className=\"flex gap-3 pt-4 border-t border-gray-200\">\r\n                <button className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\r\n                  <CheckCircle size={16} />\r\n                  Chấp nhận\r\n                </button>\r\n                <button className=\"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\">\r\n                  <XCircle size={16} />\r\n                  Từ chối\r\n                </button>\r\n                <button className=\"flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\r\n                  <Edit3 size={16} />\r\n                  Đề xuất thời gian khác\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AppointmentRequests;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAeA,MAAM,sBAAsB,CAAC,EAC3B,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,eAAe,EACU;IACzB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCACb,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;oCAAC;;;;;;;0CAEtE,8OAAC;gCAAK,WAAU;;oCACb,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,QAAQ,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;0BAKxE,8OAAC;gBAAI,WAAU;0BACZ,oBAAoB,GAAG,CAAC,CAAC,wBACxB,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAgB,MAAM;;;;;;;;;;;0DAExC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B,QAAQ,WAAW;;;;;;kEAChE,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,KAAK;;;;;;oDAClD,QAAQ,UAAU,kBACjB,8OAAC;wDAAE,WAAU;;4DAAyB,QAAQ,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;kDAI/D,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,OAAO,kBACd,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,OAAO,GAAG;0DAC/F,gBAAgB,QAAQ,OAAO;;;;;;0DAGpC,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;0DAC5F,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAc,8OAAC;wDAAK,WAAU;kEAAe,QAAQ,WAAW;;;;;;;;;;;;0DACrG,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAqB,8OAAC;wDAAK,WAAU;kEAAe,QAAQ,aAAa;;;;;;;;;;;;0DAC9G,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAM,8OAAC;wDAAK,WAAU;kEAAe,QAAQ,UAAU;;;;;;;;;;;;;;;;;;kDAE9F,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAyB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;4BAIzD,QAAQ,MAAM,KAAK,2BAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAG3B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,4MAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;4CAAM;;;;;;;kDAGvB,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,0MAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;uBAjDjB,QAAQ,EAAE;;;;;;;;;;;;;;;;AA2D9B;uCAEe", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/LeaveRequests/LeaveRequests.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Clock, Plus, Edit3, Trash2 } from 'lucide-react';\r\nimport { LeaveRequest, StatusType } from '@/hooks/doctor/types';\r\n\r\ninterface LeaveRequestsProps {\r\n  leaveRequests: LeaveRequest[];\r\n  getStatusColor: (status: StatusType) => string;\r\n  getStatusText: (status: StatusType) => string;\r\n}\r\n\r\nconst LeaveRequests = ({\r\n  leaveRequests,\r\n  getStatusColor,\r\n  getStatusText,\r\n}: LeaveRequestsProps) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-2xl font-bold text-gray-800\">Đơn xin Nghỉ</h2>\r\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\">\r\n          <Plus size={20} />\r\n          Tạo đơn xin nghỉ\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"grid gap-4\">\r\n        {leaveRequests.map((request) => (\r\n          <div key={request.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n            <div className=\"flex justify-between items-start mb-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center\">\r\n                  <Clock className=\"text-purple-600\" size={24} />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">Đơn xin nghỉ #{request.id}</h3>\r\n                  <p className=\"text-sm text-gray-500\">Ngày tạo: {request.requestDate}</p>\r\n                  <span className={`inline-block px-2 py-1 rounded text-xs font-medium mt-1 ${\r\n                    request.type === 'vacation' ? 'bg-blue-100 text-blue-800' :\r\n                    request.type === 'sick' ? 'bg-red-100 text-red-800' :\r\n                    request.type === 'conference' ? 'bg-green-100 text-green-800' :\r\n                    'bg-yellow-100 text-yellow-800'\r\n                  }`}>\r\n                    {request.type === 'vacation' ? 'Nghỉ phép' :\r\n                     request.type === 'sick' ? 'Nghỉ ốm' :\r\n                     request.type === 'conference' ? 'Hội nghị' : 'Khẩn cấp'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>\r\n                {getStatusText(request.status)}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Từ ngày: <span className=\"font-medium\">{request.startDate}</span></p>\r\n                <p className=\"text-sm text-gray-600\">Đến ngày: <span className=\"font-medium\">{request.endDate}</span></p>\r\n                <p className=\"text-sm text-gray-600\">Số ngày: <span className=\"font-medium\">\r\n                  {Math.ceil((new Date(request.endDate).getTime() - new Date(request.startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1}\r\n                </span></p>\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600 mb-2\">Lý do:</p>\r\n                <p className=\"text-sm text-gray-800\">{request.reason}</p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex gap-3 pt-4 border-t border-gray-200\">\r\n              <button className=\"flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\">\r\n                <Edit3 size={16} />\r\n                Chỉnh sửa\r\n              </button>\r\n              {request.status === 'pending' && (\r\n                <button className=\"flex items-center gap-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors\">\r\n                  <Trash2 size={16} />\r\n                  Hủy đơn\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LeaveRequests;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAYA,MAAM,gBAAgB,CAAC,EACrB,aAAa,EACb,cAAc,EACd,aAAa,EACM;IACnB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC;wBAAqB,WAAU;;0CAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAkB,MAAM;;;;;;;;;;;0DAE3C,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAA8B;4DAAe,QAAQ,EAAE;;;;;;;kEACrE,8OAAC;wDAAE,WAAU;;4DAAwB;4DAAW,QAAQ,WAAW;;;;;;;kEACnE,8OAAC;wDAAK,WAAW,CAAC,wDAAwD,EACxE,QAAQ,IAAI,KAAK,aAAa,8BAC9B,QAAQ,IAAI,KAAK,SAAS,4BAC1B,QAAQ,IAAI,KAAK,eAAe,gCAChC,iCACA;kEACC,QAAQ,IAAI,KAAK,aAAa,cAC9B,QAAQ,IAAI,KAAK,SAAS,YAC1B,QAAQ,IAAI,KAAK,eAAe,aAAa;;;;;;;;;;;;;;;;;;kDAIpD,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;kDAC5F,cAAc,QAAQ,MAAM;;;;;;;;;;;;0CAIjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAS,8OAAC;wDAAK,WAAU;kEAAe,QAAQ,SAAS;;;;;;;;;;;;0DAC9F,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAU,8OAAC;wDAAK,WAAU;kEAAe,QAAQ,OAAO;;;;;;;;;;;;0DAC7F,8OAAC;gDAAE,WAAU;;oDAAwB;kEAAS,8OAAC;wDAAK,WAAU;kEAC3D,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK;;;;;;;;;;;;;;;;;;kDAGxH,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAyB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0CAIxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,0MAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;4CAAM;;;;;;;oCAGpB,QAAQ,MAAM,KAAK,2BAClB,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;uBA/ClB,QAAQ,EAAE;;;;;;;;;;;;;;;;AAyD9B;uCAEe", "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/Profile/Profile.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { User, Mail, Phone, MapPin, Clock, Edit3 } from 'lucide-react';\r\nimport { DoctorProfile } from '@/hooks/doctor/types';\r\n\r\ninterface ProfileProps {\r\n  doctorProfile: DoctorProfile;\r\n}\r\n\r\nconst Profile = ({ doctorProfile }: ProfileProps) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h2 className=\"text-2xl font-bold text-gray-800\">H<PERSON> sơ Cá nhân</h2>\r\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\">\r\n          <Edit3 size={20} />\r\n          Chỉnh sửa\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <div className=\"w-24 h-24 bg-white rounded-full flex items-center justify-center\">\r\n              <User className=\"text-blue-600\" size={40} />\r\n            </div>\r\n            <div className=\"text-white\">\r\n              <h3 className=\"text-2xl font-bold\">{doctorProfile.name}</h3>\r\n              <p className=\"text-blue-100\">{doctorProfile.specialization}</p>\r\n              <p className=\"text-blue-200 text-sm\">{doctorProfile.experience}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-6 space-y-6\">\r\n          <div className=\"grid md:grid-cols-2 gap-6\">\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-gray-900 flex items-center gap-2 pb-2 border-b border-gray-200\">\r\n                <Mail size={20} className=\"text-blue-600\" />\r\n                Thông tin liên hệ\r\n              </h4>\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <Mail size={16} className=\"text-gray-400\" />\r\n                  <span className=\"text-sm text-gray-700\">{doctorProfile.email}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <Phone size={16} className=\"text-gray-400\" />\r\n                  <span className=\"text-sm text-gray-700\">{doctorProfile.phone}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <MapPin size={16} className=\"text-gray-400\" />\r\n                  <span className=\"text-sm text-gray-700\">{doctorProfile.address}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <Clock size={16} className=\"text-gray-400\" />\r\n                  <span className=\"text-sm text-gray-700\">Giờ làm việc: {doctorProfile.workingHours}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-gray-900 flex items-center gap-2 pb-2 border-b border-gray-200\">\r\n                <User size={20} className=\"text-purple-600\" />\r\n                Thông tin chuyên môn\r\n              </h4>\r\n              <div className=\"space-y-3\">\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600 mb-1\">Học vấn:</p>\r\n                  <p className=\"text-sm text-gray-800\">{doctorProfile.education}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600 mb-2\">Chứng chỉ:</p>\r\n                  <div className=\"space-y-1\">\r\n                    {doctorProfile.certifications.map((cert, index) => (\r\n                      <span key={index} className=\"inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2 mb-1\">\r\n                        {cert}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm text-gray-600 mb-2\">Ngôn ngữ:</p>\r\n                  <div className=\"space-y-1\">\r\n                    {doctorProfile.languages.map((lang, index) => (\r\n                      <span key={index} className=\"inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-2 mb-1\">\r\n                        {lang}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAUA,MAAM,UAAU,CAAC,EAAE,aAAa,EAAgB;IAC9C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,0MAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAgB,MAAM;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB,cAAc,IAAI;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAiB,cAAc,cAAc;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAyB,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAKpE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAkB;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC1B,8OAAC;4DAAK,WAAU;sEAAyB,cAAc,KAAK;;;;;;;;;;;;8DAE9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC3B,8OAAC;4DAAK,WAAU;sEAAyB,cAAc,KAAK;;;;;;;;;;;;8DAE9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC5B,8OAAC;4DAAK,WAAU;sEAAyB,cAAc,OAAO;;;;;;;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC3B,8OAAC;4DAAK,WAAU;;gEAAwB;gEAAe,cAAc,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;8CAKvF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAoB;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAyB,cAAc,SAAS;;;;;;;;;;;;8DAE/D,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACZ,cAAc,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;oEAAiB,WAAU;8EACzB;mEADQ;;;;;;;;;;;;;;;;8DAMjB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACZ,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;oEAAiB,WAAU;8EACzB;mEADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAajC;uCAEe", "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/DashboardStats/DashboardStats.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Calendar, CheckCircle, Clock, Users, TrendingUp, Activity, AlertCircle } from 'lucide-react';\r\nimport { DashboardStats } from '@/hooks/doctor/types';\r\n\r\ninterface DashboardStatsProps {\r\n  dashboardStats: DashboardStats;\r\n}\r\n\r\nconst DashboardStats = ({ dashboardStats }: DashboardStatsProps) => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-600\">Lịch hẹn hôm nay</p>\r\n            <p className=\"text-3xl font-bold text-gray-900\">{dashboardStats.todayAppointments}</p>\r\n          </div>\r\n          <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n            <Calendar className=\"text-blue-600\" size={24} />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center mt-4 text-sm\">\r\n          <TrendingUp className=\"text-green-500 mr-1\" size={16} />\r\n          <span className=\"text-green-600\">+12% so với hôm qua</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-600\">Đã hoàn thành</p>\r\n            <p className=\"text-3xl font-bold text-gray-900\">{dashboardStats.completedToday}</p>\r\n          </div>\r\n          <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\r\n            <CheckCircle className=\"text-green-600\" size={24} />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center mt-4 text-sm\">\r\n          <Activity className=\"text-blue-500 mr-1\" size={16} />\r\n          <span className=\"text-gray-600\">{Math.round((dashboardStats.completedToday / dashboardStats.todayAppointments) * 100)}% tỷ lệ hoàn thành</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-600\">Yêu cầu chờ xử lý</p>\r\n            <p className=\"text-3xl font-bold text-gray-900\">{dashboardStats.pendingRequests}</p>\r\n          </div>\r\n          <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\r\n            <Clock className=\"text-yellow-600\" size={24} />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center mt-4 text-sm\">\r\n          <AlertCircle className=\"text-yellow-500 mr-1\" size={16} />\r\n          <span className=\"text-yellow-600\">Cần xử lý trong hôm nay</span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-600\">Sắp tới</p>\r\n            <p className=\"text-3xl font-bold text-gray-900\">{dashboardStats.upcomingAppointments}</p>\r\n          </div>\r\n          <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\r\n            <Users className=\"text-purple-600\" size={24} />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center mt-4 text-sm\">\r\n          <Clock className=\"text-purple-500 mr-1\" size={16} />\r\n          <span className=\"text-gray-600\">Trong tuần này</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardStats;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAUA,MAAM,iBAAiB,CAAC,EAAE,cAAc,EAAuB;IAC7D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAoC,eAAe,iBAAiB;;;;;;;;;;;;0CAEnF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAgB,MAAM;;;;;;;;;;;;;;;;;kCAG9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;gCAAsB,MAAM;;;;;;0CAClD,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;0BAIrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAoC,eAAe,cAAc;;;;;;;;;;;;0CAEhF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;oCAAiB,MAAM;;;;;;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAqB,MAAM;;;;;;0CAC/C,8OAAC;gCAAK,WAAU;;oCAAiB,KAAK,KAAK,CAAC,AAAC,eAAe,cAAc,GAAG,eAAe,iBAAiB,GAAI;oCAAK;;;;;;;;;;;;;;;;;;;0BAI1H,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAoC,eAAe,eAAe;;;;;;;;;;;;0CAEjF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAkB,MAAM;;;;;;;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;gCAAuB,MAAM;;;;;;0CACpD,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAoC,eAAe,oBAAoB;;;;;;;;;;;;0CAEtF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAkB,MAAM;;;;;;;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;gCAAuB,MAAM;;;;;;0CAC9C,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAK1C;uCAEe", "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/Sidebar/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { User } from 'lucide-react';\r\nimport { MenuItem } from './types';\r\n\r\ninterface SidebarProps {\r\n  menuItems: MenuItem[];\r\n  activeTab: string;\r\n  onTabChange: (tab: string) => void;\r\n}\r\n\r\nconst Sidebar = ({ menuItems, activeTab, onTabChange }: SidebarProps) => {\r\n  return (\r\n    <div className=\"w-64 bg-white shadow-lg min-h-screen\">\r\n      <div className=\"p-6 border-b border-gray-200\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\">\r\n            <User className=\"text-white\" size={24} />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"font-bold text-gray-800\">Hệ thống <PERSON> s<PERSON></h2>\r\n            <p className=\"text-sm text-gray-500\">BS. Trần Thị Mai</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <nav className=\"p-4\">\r\n        <ul className=\"space-y-2\">\r\n          {menuItems.map((item) => {\r\n            const Icon = item.icon;\r\n            return (\r\n              <li key={item.id}>\r\n                <button\r\n                  onClick={() => onTabChange(item.id)}\r\n                  className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-colors relative ${\r\n                    activeTab === item.id\r\n                      ? 'bg-blue-100 text-blue-700 font-medium'\r\n                      : 'text-gray-600 hover:bg-gray-100'\r\n                  }`}\r\n                >\r\n                  <Icon size={20} />\r\n                  {item.label}\r\n                  {item.badge && item.badge > 0 && (\r\n                    <span className=\"absolute right-3 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\r\n                      {item.badge}\r\n                    </span>\r\n                  )}\r\n                </button>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAYA,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAgB;IAClE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAa,MAAM;;;;;;;;;;;sCAErC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAK3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,UAAU,GAAG,CAAC,CAAC;wBACd,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,8OAAC;sCACC,cAAA,8OAAC;gCACC,SAAS,IAAM,YAAY,KAAK,EAAE;gCAClC,WAAW,CAAC,yFAAyF,EACnG,cAAc,KAAK,EAAE,GACjB,0CACA,mCACJ;;kDAEF,8OAAC;wCAAK,MAAM;;;;;;oCACX,KAAK,KAAK;oCACV,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;2BAbV,KAAK,EAAE;;;;;oBAmBpB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctor/Header/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { User, Bell } from 'lucide-react';\r\nimport { DashboardStats } from '@/hooks/doctor/types';\r\n\r\ninterface HeaderProps {\r\n  dashboardStats: DashboardStats;\r\n  doctorName: string;\r\n  showNotifications: boolean;\r\n  onToggleNotifications: () => void;\r\n}\r\n\r\nconst Header = ({ dashboardStats, doctorName, showNotifications, onToggleNotifications }: HeaderProps) => {\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b border-gray-200 px-6 py-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">Dashboard Bác sĩ</h1>\r\n          <p className=\"text-gray-600\">Hôm nay: {new Date().toLocaleDateString('vi-VN', { \r\n            weekday: 'long', \r\n            year: 'numeric', \r\n            month: 'long', \r\n            day: 'numeric' \r\n          })}</p>\r\n        </div>\r\n        <div className=\"flex items-center gap-4\">\r\n          <button \r\n            onClick={onToggleNotifications}\r\n            className=\"relative p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\r\n          >\r\n            <Bell size={20} />\r\n            {dashboardStats.pendingRequests > 0 && (\r\n              <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\r\n                {dashboardStats.pendingRequests}\r\n              </span>\r\n            )}\r\n          </button>\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\r\n              <User className=\"text-white\" size={16} />\r\n            </div>\r\n            <span className=\"text-sm font-medium text-gray-700\">{doctorName}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAHA;;;AAaA,MAAM,SAAS,CAAC,EAAE,cAAc,EAAE,UAAU,EAAE,iBAAiB,EAAE,qBAAqB,EAAe;IACnG,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;;gCAAgB;gCAAU,IAAI,OAAO,kBAAkB,CAAC,SAAS;oCAC5E,SAAS;oCACT,MAAM;oCACN,OAAO;oCACP,KAAK;gCACP;;;;;;;;;;;;;8BAEF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;gCACX,eAAe,eAAe,GAAG,mBAChC,8OAAC;oCAAK,WAAU;8CACb,eAAe,eAAe;;;;;;;;;;;;sCAIrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAa,MAAM;;;;;;;;;;;8CAErC,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjE;uCAEe", "debugId": null}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28doctor%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport ScheduleManagement from '@/components/doctor/ScheduleManagement/ScheduleManagement';\r\nimport AppointmentRequests from '@/components/doctor/AppointmentRequests/AppointmentRequests';\r\nimport LeaveRequests from '@/components/doctor/LeaveRequests/LeaveRequests';\r\nimport Profile from '@/components/doctor/Profile/Profile';\r\nimport DashboardStatsComponent from '@/components/doctor/DashboardStats/DashboardStats'; \r\nimport Sidebar from '@/components/doctor/Sidebar/Sidebar';\r\nimport Header from '@/components/doctor/Header/Header';\r\nimport { \r\n  Calendar, \r\n  Users, \r\n  Clock, \r\n  User, \r\n} from 'lucide-react'; // Removed unused 'Search' import\r\n\r\ntype StatusType = 'confirmed' | 'pending' | 'completed' | 'approved' | 'rejected' | 'cancelled';\r\n\r\ninterface ScheduleItem {\r\n  id: number;\r\n  patientName: string;\r\n  phone: string;\r\n  time: string;\r\n  department: string;\r\n  symptoms: string;\r\n  status: StatusType;\r\n  duration?: number;\r\n  patientAge?: number;\r\n  priority?: 'low' | 'medium' | 'high';\r\n}\r\n\r\ninterface AppointmentRequest {\r\n  id: number;\r\n  patientName: string;\r\n  phone: string;\r\n  requestedTime: string;\r\n  department: string;\r\n  symptoms: string;\r\n  status: StatusType;\r\n  requestDate: string;\r\n  patientAge?: number;\r\n  urgency?: 'low' | 'medium' | 'high';\r\n}\r\n\r\ninterface LeaveRequest {\r\n  id: number;\r\n  startDate: string;\r\n  endDate: string;\r\n  reason: string;\r\n  status: StatusType;\r\n  requestDate: string;\r\n  type: 'vacation' | 'sick' | 'conference' | 'emergency';\r\n}\r\n\r\ninterface DoctorProfile {\r\n  name: string;\r\n  specialization: string;\r\n  experience: string;\r\n  email: string;\r\n  phone: string;\r\n  address: string;\r\n  education: string;\r\n  certifications: string[];\r\n  avatar?: string;\r\n  workingHours: string;\r\n  languages: string[];\r\n}\r\n\r\ninterface MenuItem {\r\n  id: string;\r\n  label: string;\r\n  icon: React.ComponentType<{ size?: number; className?: string }>;\r\n  badge?: number;\r\n}\r\n\r\ninterface DashboardStats {\r\n  todayAppointments: number;\r\n  completedToday: number;\r\n  pendingRequests: number;\r\n  upcomingAppointments: number;\r\n}\r\n\r\nconst DoctorDashboard = () => {\r\n  const [activeTab, setActiveTab] = useState('schedule');\r\n  const [selectedDate, setSelectedDate] = useState('2025-06-11');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filterStatus, setFilterStatus] = useState<StatusType | 'all'>('all');\r\n  const [showNotifications, setShowNotifications] = useState(false);\r\n\r\n  const scheduleData: ScheduleItem[] = [\r\n    { id: 1, patientName: 'Nguyễn Văn An', phone: '**********', time: '09:00', department: 'Nội khoa', symptoms: 'Đau đầu, chóng mặt, mệt mỏi', status: 'confirmed', duration: 30, patientAge: 45, priority: 'medium' },\r\n    { id: 2, patientName: 'Lê Thị Bình', phone: '**********', time: '10:30', department: 'Tim mạch', symptoms: 'Đau ngực, khó thở, tim đập nhanh', status: 'pending', duration: 45, patientAge: 58, priority: 'high' },\r\n    { id: 3, patientName: 'Hoàng Minh Tuấn', phone: '**********', time: '11:00', department: 'Ngoại khoa', symptoms: 'Đau bụng dưới bên phải, buồn nôn', status: 'confirmed', duration: 60, patientAge: 32, priority: 'high' },\r\n    { id: 4, patientName: 'Phạm Thị Lan', phone: '**********', time: '14:00', department: 'Nội khoa', symptoms: 'Ho khan, khó thở, đau ngực', status: 'completed', duration: 30, patientAge: 67, priority: 'medium' },\r\n    { id: 5, patientName: 'Võ Thanh Hùng', phone: '**********', time: '15:30', department: 'Tim mạch', symptoms: 'Kiểm tra định kỳ, cao huyết áp', status: 'confirmed', duration: 20, patientAge: 55, priority: 'low' },\r\n  ];\r\n\r\n  const appointmentRequests: AppointmentRequest[] = [\r\n    { id: 1, patientName: 'Trần Văn Hòa', phone: '**********', requestedTime: '15:30', department: 'Tim mạch', symptoms: 'Đau tim, khó thở khi gắng sức', status: 'pending', requestDate: '2025-06-12', patientAge: 62, urgency: 'high' },\r\n    { id: 2, patientName: 'Vũ Thị Mai', phone: '**********', requestedTime: '16:00', department: 'Nội khoa', symptoms: 'Sốt cao, đau họng, ho', status: 'pending', requestDate: '2025-06-12', patientAge: 28, urgency: 'medium' },\r\n    { id: 3, patientName: 'Đặng Minh Quân', phone: '**********', requestedTime: '09:00', department: 'Ngoại khoa', symptoms: 'Đau lưng, tê chân, khó đi lại', status: 'pending', requestDate: '2025-06-13', patientAge: 41, urgency: 'high' },\r\n  ];\r\n\r\n  const leaveRequests: LeaveRequest[] = [\r\n    { id: 1, startDate: '2025-06-15', endDate: '2025-06-17', reason: 'Nghỉ phép cá nhân', status: 'approved', requestDate: '2025-06-05', type: 'vacation' },\r\n    { id: 2, startDate: '2025-07-01', endDate: '2025-07-05', reason: 'Tham gia hội nghị y khoa', status: 'pending', requestDate: '2025-06-10', type: 'conference' },\r\n    { id: 3, startDate: '2025-06-20', endDate: '2025-06-20', reason: 'Khám sức khỏe định kỳ', status: 'approved', requestDate: '2025-06-08', type: 'sick' },\r\n  ];\r\n\r\n  const doctorProfile: DoctorProfile = {\r\n    name: 'BS. Trần Thị Mai',\r\n    specialization: 'Bác sĩ chuyên khoa Tim mạch',\r\n    experience: '10 năm kinh nghiệm',\r\n    email: '<EMAIL>',\r\n    phone: '**********',\r\n    address: 'Bệnh viện Đa khoa Trung ương',\r\n    education: 'Tiến sĩ Y học - Đại học Y Hà Nội',\r\n    certifications: ['Chứng chỉ Tim mạch can thiệp', 'Chứng chỉ Siêu âm tim', 'Chứng chỉ Cấp cứu tim mạch'],\r\n    workingHours: '7:00 - 17:00',\r\n    languages: ['Tiếng Việt', 'English', '中文'],\r\n  };\r\n\r\n  const dashboardStats: DashboardStats = useMemo(() => {\r\n    const todayAppointments = scheduleData.length;\r\n    const completedToday = scheduleData.filter(item => item.status === 'completed').length;\r\n    const pendingRequests = appointmentRequests.filter(req => req.status === 'pending').length;\r\n    const upcomingAppointments = scheduleData.filter(item => \r\n      item.status === 'confirmed' || item.status === 'pending'\r\n    ).length;\r\n\r\n    return { todayAppointments, completedToday, pendingRequests, upcomingAppointments };\r\n  }, [scheduleData, appointmentRequests]);\r\n\r\n  const menuItems: MenuItem[] = [\r\n    { id: 'schedule', label: 'Quản lý Lịch hẹn', icon: Calendar, badge: dashboardStats.upcomingAppointments },\r\n    { id: 'appointments', label: 'Yêu cầu Khám bệnh', icon: Users, badge: dashboardStats.pendingRequests },\r\n    { id: 'leave', label: 'Đơn xin Nghỉ', icon: Clock },\r\n    { id: 'profile', label: 'Hồ sơ Cá nhân', icon: User },\r\n  ];\r\n\r\n  const getStatusColor = (status: StatusType): string => {\r\n    switch (status) {\r\n      case 'confirmed': return 'text-green-600 bg-green-100';\r\n      case 'pending': return 'text-yellow-600 bg-yellow-100';\r\n      case 'completed': return 'text-blue-600 bg-blue-100';\r\n      case 'approved': return 'text-green-600 bg-green-100';\r\n      case 'rejected': return 'text-red-600 bg-red-100';\r\n      case 'cancelled': return 'text-gray-600 bg-gray-100';\r\n      default: return 'text-gray-600 bg-gray-100';\r\n    }\r\n  };\r\n\r\n  const getStatusText = (status: StatusType): string => {\r\n    switch (status) {\r\n      case 'confirmed': return 'Đã xác nhận';\r\n      case 'pending': return 'Chờ xử lý';\r\n      case 'completed': return 'Hoàn thành';\r\n      case 'approved': return 'Đã duyệt';\r\n      case 'rejected': return 'Từ chối';\r\n      case 'cancelled': return 'Đã hủy';\r\n      default: return status;\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string): string => {\r\n    switch (priority) {\r\n      case 'high': return 'text-red-600 bg-red-100';\r\n      case 'medium': return 'text-yellow-600 bg-yellow-100';\r\n      case 'low': return 'text-green-600 bg-green-100';\r\n      default: return 'text-gray-600 bg-gray-100';\r\n    }\r\n  };\r\n\r\n  const getPriorityText = (priority: string): string => {\r\n    switch (priority) {\r\n      case 'high': return 'Cao';\r\n      case 'medium': return 'Trung bình';\r\n      case 'low': return 'Thấp';\r\n      default: return priority;\r\n    }\r\n  };\r\n\r\n  const renderContent = () => {\r\n    switch (activeTab) {\r\n      case 'schedule':\r\n        return <ScheduleManagement\r\n          scheduleData={scheduleData}\r\n          selectedDate={selectedDate}\r\n          searchTerm={searchTerm}\r\n          filterStatus={filterStatus}\r\n          onDateChange={setSelectedDate}\r\n          onSearchChange={setSearchTerm}\r\n          onFilterChange={setFilterStatus}\r\n          getStatusColor={getStatusColor}\r\n          getStatusText={getStatusText}\r\n          getPriorityColor={getPriorityColor}\r\n          getPriorityText={getPriorityText}\r\n        />;\r\n      case 'appointments':\r\n        return <AppointmentRequests\r\n          appointmentRequests={appointmentRequests}\r\n          getStatusColor={getStatusColor}\r\n          getStatusText={getStatusText}\r\n          getPriorityColor={getPriorityColor}\r\n          getPriorityText={getPriorityText}\r\n        />;\r\n      case 'leave':\r\n        return <LeaveRequests\r\n          leaveRequests={leaveRequests}\r\n          getStatusColor={getStatusColor}\r\n          getStatusText={getStatusText}\r\n        />;\r\n      case 'profile':\r\n        return <Profile doctorProfile={doctorProfile} />;\r\n      default:\r\n        return <ScheduleManagement\r\n          scheduleData={scheduleData}\r\n          selectedDate={selectedDate}\r\n          searchTerm={searchTerm}\r\n          filterStatus={filterStatus}\r\n          onDateChange={setSelectedDate}\r\n          onSearchChange={setSearchTerm}\r\n          onFilterChange={setFilterStatus}\r\n          getStatusColor={getStatusColor}\r\n          getStatusText={getStatusText}\r\n          getPriorityColor={getPriorityColor}\r\n          getPriorityText={getPriorityText}\r\n        />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div className=\"flex\">\r\n        <Sidebar menuItems={menuItems} activeTab={activeTab} onTabChange={setActiveTab} />\r\n        <div className=\"flex-1 min-h-screen\">\r\n          <Header\r\n            dashboardStats={dashboardStats}\r\n            doctorName={doctorProfile.name}\r\n            showNotifications={showNotifications}\r\n            onToggleNotifications={() => setShowNotifications(!showNotifications)}\r\n          />\r\n          <main className=\"p-6\">\r\n            {activeTab === 'schedule' && <DashboardStatsComponent dashboardStats={dashboardStats} />}\r\n            {renderContent()}\r\n          </main>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DoctorDashboard;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gWAKuB,iCAAiC;AALxD;AAAA;AAAA;AAVA;;;;;;;;;;;AAmFA,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,eAA+B;QACnC;YAAE,IAAI;YAAG,aAAa;YAAiB,OAAO;YAAc,MAAM;YAAS,YAAY;YAAY,UAAU;YAA+B,QAAQ;YAAa,UAAU;YAAI,YAAY;YAAI,UAAU;QAAS;QAClN;YAAE,IAAI;YAAG,aAAa;YAAe,OAAO;YAAc,MAAM;YAAS,YAAY;YAAY,UAAU;YAAoC,QAAQ;YAAW,UAAU;YAAI,YAAY;YAAI,UAAU;QAAO;QACjN;YAAE,IAAI;YAAG,aAAa;YAAmB,OAAO;YAAc,MAAM;YAAS,YAAY;YAAc,UAAU;YAAoC,QAAQ;YAAa,UAAU;YAAI,YAAY;YAAI,UAAU;QAAO;QACzN;YAAE,IAAI;YAAG,aAAa;YAAgB,OAAO;YAAc,MAAM;YAAS,YAAY;YAAY,UAAU;YAA8B,QAAQ;YAAa,UAAU;YAAI,YAAY;YAAI,UAAU;QAAS;QAChN;YAAE,IAAI;YAAG,aAAa;YAAiB,OAAO;YAAc,MAAM;YAAS,YAAY;YAAY,UAAU;YAAkC,QAAQ;YAAa,UAAU;YAAI,YAAY;YAAI,UAAU;QAAM;KACnN;IAED,MAAM,sBAA4C;QAChD;YAAE,IAAI;YAAG,aAAa;YAAgB,OAAO;YAAc,eAAe;YAAS,YAAY;YAAY,UAAU;YAAiC,QAAQ;YAAW,aAAa;YAAc,YAAY;YAAI,SAAS;QAAO;QACpO;YAAE,IAAI;YAAG,aAAa;YAAc,OAAO;YAAc,eAAe;YAAS,YAAY;YAAY,UAAU;YAAyB,QAAQ;YAAW,aAAa;YAAc,YAAY;YAAI,SAAS;QAAS;QAC5N;YAAE,IAAI;YAAG,aAAa;YAAkB,OAAO;YAAc,eAAe;YAAS,YAAY;YAAc,UAAU;YAAiC,QAAQ;YAAW,aAAa;YAAc,YAAY;YAAI,SAAS;QAAO;KACzO;IAED,MAAM,gBAAgC;QACpC;YAAE,IAAI;YAAG,WAAW;YAAc,SAAS;YAAc,QAAQ;YAAqB,QAAQ;YAAY,aAAa;YAAc,MAAM;QAAW;QACtJ;YAAE,IAAI;YAAG,WAAW;YAAc,SAAS;YAAc,QAAQ;YAA4B,QAAQ;YAAW,aAAa;YAAc,MAAM;QAAa;QAC9J;YAAE,IAAI;YAAG,WAAW;YAAc,SAAS;YAAc,QAAQ;YAAyB,QAAQ;YAAY,aAAa;YAAc,MAAM;QAAO;KACvJ;IAED,MAAM,gBAA+B;QACnC,MAAM;QACN,gBAAgB;QAChB,YAAY;QACZ,OAAO;QACP,OAAO;QACP,SAAS;QACT,WAAW;QACX,gBAAgB;YAAC;YAAgC;YAAyB;SAA6B;QACvG,cAAc;QACd,WAAW;YAAC;YAAc;YAAW;SAAK;IAC5C;IAEA,MAAM,iBAAiC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7C,MAAM,oBAAoB,aAAa,MAAM;QAC7C,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,MAAM;QACtF,MAAM,kBAAkB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM;QAC1F,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA,OAC/C,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,WAC/C,MAAM;QAER,OAAO;YAAE;YAAmB;YAAgB;YAAiB;QAAqB;IACpF,GAAG;QAAC;QAAc;KAAoB;IAEtC,MAAM,YAAwB;QAC5B;YAAE,IAAI;YAAY,OAAO;YAAoB,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO,eAAe,oBAAoB;QAAC;QACxG;YAAE,IAAI;YAAgB,OAAO;YAAqB,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO,eAAe,eAAe;QAAC;QACrG;YAAE,IAAI;YAAS,OAAO;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAClD;YAAE,IAAI;YAAW,OAAO;YAAiB,MAAM,kMAAA,CAAA,OAAI;QAAC;KACrD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wKAAA,CAAA,UAAkB;oBACxB,cAAc;oBACd,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB;oBAChB,gBAAgB;oBAChB,eAAe;oBACf,kBAAkB;oBAClB,iBAAiB;;;;;;YAErB,KAAK;gBACH,qBAAO,8OAAC,0KAAA,CAAA,UAAmB;oBACzB,qBAAqB;oBACrB,gBAAgB;oBAChB,eAAe;oBACf,kBAAkB;oBAClB,iBAAiB;;;;;;YAErB,KAAK;gBACH,qBAAO,8OAAC,8JAAA,CAAA,UAAa;oBACnB,eAAe;oBACf,gBAAgB;oBAChB,eAAe;;;;;;YAEnB,KAAK;gBACH,qBAAO,8OAAC,kJAAA,CAAA,UAAO;oBAAC,eAAe;;;;;;YACjC;gBACE,qBAAO,8OAAC,wKAAA,CAAA,UAAkB;oBACxB,cAAc;oBACd,cAAc;oBACd,YAAY;oBACZ,cAAc;oBACd,cAAc;oBACd,gBAAgB;oBAChB,gBAAgB;oBAChB,gBAAgB;oBAChB,eAAe;oBACf,kBAAkB;oBAClB,iBAAiB;;;;;;QAEvB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kJAAA,CAAA,UAAO;oBAAC,WAAW;oBAAW,WAAW;oBAAW,aAAa;;;;;;8BAClE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gJAAA,CAAA,UAAM;4BACL,gBAAgB;4BAChB,YAAY,cAAc,IAAI;4BAC9B,mBAAmB;4BACnB,uBAAuB,IAAM,qBAAqB,CAAC;;;;;;sCAErD,8OAAC;4BAAK,WAAU;;gCACb,cAAc,4BAAc,8OAAC,gKAAA,CAAA,UAAuB;oCAAC,gBAAgB;;;;;;gCACrE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}]}